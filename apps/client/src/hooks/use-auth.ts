import { useEffect, useState } from 'react'
import { getJwt } from '~/modules/auth/jwt-storage'
import { calculateServerUrl } from '~/modules/urls/url-builder'

export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated'

const loginUrl = calculateServerUrl('/api/users/me')

export interface User {
  id: string
  name: string
}

export interface AuthContext {
  isAuthenticated: AuthStatus
  user: User | null
  login: (jwt: string) => void
  logout: () => void
  refetch: () => Promise<void>
}

export function useAuth(): AuthContext {
  const [authStatus, setAuthStatus] = useState<AuthStatus>('loading')
  const [user, setUser] = useState<User | null>(null)

  const fetchUser = async (): Promise<void> => {
    try {
      const jwt = getJwt()

      if (!jwt) {
        setAuthStatus('unauthenticated')
        setUser(null)
        return
      }

      const response = await fetch(loginUrl, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${jwt}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success && result.data) {
          setUser(result.data)
          setAuthStatus('authenticated')
        } else {
          setAuthStatus('unauthenticated')
          setUser(null)
        }
      } else {
        setAuthStatus('unauthenticated')
        setUser(null)
      }
    } catch (error) {
      console.error('Error fetching user:', error)
      setAuthStatus('unauthenticated')
      setUser(null)
    }
  }

  const login = (jwt: string) => {
    // Store JWT in cookie and localStorage
    document.cookie = `jwt=${jwt}; path=/; max-age=${30 * 24 * 60 * 60}` // 30 days
    localStorage.setItem('jwt', jwt)

    // Refetch user data
    fetchUser()
  }

  const logout = () => {
    // Remove JWT from cookie and localStorage
    document.cookie = 'jwt=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    localStorage.removeItem('jwt')

    setAuthStatus('unauthenticated')
    setUser(null)
  }

  useEffect(() => {
    fetchUser()
  }, [])

  return {
    isAuthenticated: authStatus,
    user,
    login,
    logout,
    refetch: fetchUser,
  }
}
