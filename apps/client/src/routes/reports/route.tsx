import { Link, Outlet, createFileRoute } from '@tanstack/react-router'
import { <PERSON><PERSON><PERSON>, Clock } from 'lucide-react'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '~/components/ui/sidebar'

export const Route = createFileRoute('/reports')({
  component: ReportsLayout,
})

function ReportsLayout() {
  return (
    <div>
      <SidebarProvider>
        <ReportsSidebar />
        <SidebarTrigger />

        <Outlet />
      </SidebarProvider>
    </div>
  )
}

function ReportsSidebar() {
  return (
    <Sidebar>
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuButton asChild>
            <Link to="/" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Back to App</span>
            </Link>
          </SidebarMenuButton>
        </SidebarMenu>

        <SidebarGroup>
          <SidebarGroupLabel>Reports</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <Link
                  to="/reports/timerecord-aggregate"
                  className="flex items-center gap-2"
                  activeProps={{ className: 'bg-accent' }}
                >
                  <BarChart className="h-4 w-4" />
                  <span>Time Aggregation</span>
                </Link>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
