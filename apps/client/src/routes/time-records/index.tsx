import type { Schema } from '@ftt/shared'
import { useQ<PERSON>y, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import * as v from 'valibot'

import {
  type TimeRecord,
  TimeRecordEditor,
} from '~/components/time-record-editor'
import { TimeRecordsAggregateInfo } from '~/components/time-records-aggregate-info'
import { TimerCreator } from '~/components/timer-creator'
import { useKeyboardNavigation } from '~/lib/hooks/use-keyboard-navigation'
import { useRefify } from '~/lib/hooks/use-refify'
import { stopTimer } from '~/lib/model/app-state'
import { getDateRange, getTodayDateString } from '~/lib/utils/date-time'
import { DateFilterControls } from '~/modules/timerecords/components/date-filter-controls'
import { useTimeRecords } from '~/modules/timerecords/hooks/use-time-records-with-remote'
import {
  KeyboardNavigationHelp,
  TimeRecordsHeader,
} from '../../modules/timerecords/components/time-records-header'
import {
  TimeRecordsTable,
  type TimeRecordsTableRef,
} from '../../modules/timerecords/components/time-records-table'

const searchSchema = v.object({
  date: v.optional(v.string()),
  selectedCustomerId: v.optional(v.pipe(v.string(), v.uuid())),
  selectedProjectId: v.optional(v.pipe(v.string(), v.uuid())),
  selectedTaskId: v.optional(v.pipe(v.string(), v.uuid())),
  selectedTimeRecordId: v.optional(v.pipe(v.string(), v.uuid())),
})

export const Route = createFileRoute('/time-records/')({
  component: RouteComponent,
  validateSearch: searchSchema,
})

/**
 * Main component for the time records route
 */
function RouteComponent() {
  const {
    date,
    selectedCustomerId,
    selectedProjectId,
    selectedTaskId,
    selectedTimeRecordId: urlSelectedTimeRecordId,
  } = Route.useSearch()
  const navigate = Route.useNavigate()
  const z = useZero<Schema>()

  // State for selected row index
  // const [selectedRowIndex, setSelectedRowIndex] = useState<number>(-1)

  // Set default date to today if not provided
  const selectedDate = date ?? getTodayDateString()

  // Calculate date range for filtering
  const { selectedStart, selectedEnd } = useMemo(
    () => getDateRange(selectedDate),
    [selectedDate]
  )

  // State for dialogs and selected time record
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const [isTimerDialogOpen, setIsTimerDialogOpen] = useState(false)
  // const [selectedTimeRecordId, setSelectedTimeRecordId] = useState<
  //   string | null
  // >(null)

  // Create refs for values needed in event handlers
  const navigateRef = useRef(navigate)
  navigateRef.current = navigate

  const selectedDateRef = useRef(selectedDate)
  selectedDateRef.current = selectedDate

  // Ref for the table component
  const tableRef = useRef<TimeRecordsTableRef>(null)

  // Fetch and manage time records
  const { timeRecords, saveTimeRecord, deleteTimeRecord } = useTimeRecords({
    z,
    selectedStart,
    selectedEnd,
  })

  const selectedRowIndex = useMemo(
    () => timeRecords.findIndex((it) => it.id === urlSelectedTimeRecordId),
    [timeRecords, urlSelectedTimeRecordId]
  )

  const setSelectedTimeRecordId = useCallback(
    (id: string | undefined) => {
      navigate({
        search: (oldSearch) => ({
          ...oldSearch,
          selectedTimeRecordId: id,
        }),
        resetScroll: false,
        replace: true,
      })
    },
    [navigate]
  )

  const timeRecordsRef = useRefify(timeRecords)
  const selectedRowIndexRef = useRefify(selectedRowIndex)

  const openEditTimeRecordDialog = useCallback(
    (id: string | undefined) => {
      setSelectedTimeRecordId(id)
      setTimeout(() => {
        // ensure by the time we open the editor the url state has already pointed to the correct worklog
        setIsEditorOpen(true)
        console.log('open edit time record dialog', id)
      }, 100)
    },
    [setSelectedTimeRecordId]
  )

  /**
   * Handle action on the selected row
   */
  const handleRowAction = useCallback(
    (index: number) => {
      if (index >= 0 && index < timeRecordsRef.current.length) {
        const selectedRecord = timeRecordsRef.current[index]
        openEditTimeRecordDialog(selectedRecord.id)
      }
    },
    [timeRecordsRef, openEditTimeRecordDialog]
  )

  /**
   * Handle date filter change
   */
  function handleDateChange(newDate: string): void {
    console.log('date changed -> navigate', newDate)
    navigate({
      search: (prev: { date?: string }) => ({
        ...prev,
        date: newDate,
        selectedTimeRecordId: undefined, // Reset selection when changing dates
      }),
      resetScroll: false,
      replace: true,
    })
  }

  /**
   * Clear the date filter
   */
  function clearDateFilter(): void {
    console.log('clear date filter->navigate')
    navigate({
      search: (prev: { date?: string }) => ({
        ...prev,
        date: undefined,
        selectedTimeRecordId: undefined, // Reset selection when clearing date filter
      }),
      resetScroll: false,
      replace: true,
    })
  }

  const [appState, appStateDetails] = useQuery(
    z.query.appState
      .where('id', '=', z.userID)
      .related('runningTimer', (q) =>
        q.related('timerecord', (q) =>
          q.related('task', (q) => q.related('project'))
        )
      )
      .one(),
    {
      ttl: 'forever',
    }
  )

  // Get the running timer from the store
  const runningTimer =
    appStateDetails.type === 'complete' ? appState?.runningTimer : undefined

  const durationOfRunningTimeRecord = runningTimer
    ? Date.now() - runningTimer.startTimestamp
    : undefined

  // State for real-time timer updates
  const [currentTime, setCurrentTime] = useState(Date.now())

  const handleStopTimer = useCallback(() => {
    if (runningTimer) {
      stopTimer(z, runningTimer.id, Date.now()).catch((error) => {
        console.error('Error stopping timer:', error)
      })
    }
  }, [z, runningTimer])

  /**
   * Open the time record editor for the currently running timer
   */
  const openRunningTimerEditor = useCallback(() => {
    if (runningTimer?.worklogId) {
      openEditTimeRecordDialog(runningTimer.worklogId)
    }
  }, [runningTimer?.worklogId, openEditTimeRecordDialog])

  // Set up keyboard navigation
  const { handleTableKeyDown } = useKeyboardNavigation({
    navigateRef,
    selectedDateRef,
    timeRecordsRef,
    selectedRowIndexRef,
    setSelectedTimeRecordId,
    handleRowAction,
    setIsTimerDialogOpen,
    handleStopTimer,
    tableRef,
    openRunningTimerEditor,
  })

  const taskIdRef = useRefify(selectedTaskId)

  // on initial render try and select the id of the currently running task
  useEffect(() => {
    if (runningTimer && !taskIdRef.current) {
      navigate({
        search: (prev) => ({
          ...prev,
          selectedCustomerId:
            runningTimer.timerecord?.task?.project?.customerId,
          selectedProjectId: runningTimer.timerecord?.task?.project?.id,
          selectedTaskId: runningTimer.timerecord?.task?.id,
        }),
        replace: true,
        resetScroll: false,
      })
    }
  }, [taskIdRef, runningTimer, navigate])

  useEffect(() => {
    if (!runningTimer) {
      return
    }

    const intervalId = setInterval(() => {
      setCurrentTime(Date.now())
    }, 1000)

    return () => clearInterval(intervalId)
  }, [runningTimer])

  const editingTimeRecord =
    (timeRecords.find(
      (record) => record.id === urlSelectedTimeRecordId
    ) as TimeRecord) || null

  const onEditorClose = useCallback(() => setIsEditorOpen(false), [])

  if (isEditorOpen) {
    console.log('editingTimeRecord', editingTimeRecord, urlSelectedTimeRecordId)
  }

  return (
    <div className="w-full max-w-6xl mx-auto">
      <TimeRecordsAggregateInfo
        onCustomerChange={(newCustomerId) => {
          console.log('customer change', newCustomerId)
          navigate({
            search: (prev) => ({
              ...prev,
              selectedCustomerId: newCustomerId,
              selectedProjectId: undefined,
              selectedTaskId: undefined,
            }),
            resetScroll: false,
            replace: true,
          })
        }}
        onProjectChange={(newProjectId) => {
          navigate({
            search: (prev) => ({
              ...prev,
              selectedProjectId: newProjectId,
              selectedTaskId: undefined,
            }),
            resetScroll: false,
            replace: true,
          })
        }}
        onTaskChange={(newTaskId) => {
          navigate({
            search: (prev) => ({
              ...prev,
              selectedTaskId: newTaskId,
            }),
            resetScroll: false,
            replace: true,
          })
        }}
        timeRecords={timeRecords as unknown as TimeRecord[]}
        runningTimer={runningTimer}
        durationOfRunningTimeRecord={durationOfRunningTimeRecord}
        selectedCustomerId={selectedCustomerId}
        selectedProjectId={selectedProjectId}
        selectedTaskId={selectedTaskId}
      />
      {/* Header with action buttons */}
      <TimeRecordsHeader
        onCreateTimer={() => setIsTimerDialogOpen(true)}
        onAddTimeRecord={() => {
          console.log('adding new time record')
          setSelectedTimeRecordId(undefined)
          setIsEditorOpen(true)
        }}
        createTimerEnabled={!runningTimer}
      />

      {/* Keyboard navigation help */}
      <KeyboardNavigationHelp />

      {/* Date filter controls */}
      <DateFilterControls
        selectedDate={selectedDate}
        onDateChanged={handleDateChange}
        clearDateFilter={clearDateFilter}
        showClearButton={!!date}
      />

      <TimeRecordsTable
        ref={tableRef}
        timeRecords={timeRecords as unknown as TimeRecord[]}
        selectedRowId={urlSelectedTimeRecordId}
        runningTimer={runningTimer}
        currentTime={currentTime}
        onRowClick={setSelectedTimeRecordId}
        onRowDoubleClick={(id) => {
          const index = timeRecords.findIndex((record) => record.id === id)
          if (index !== -1) {
            handleRowAction(index)
          }
        }}
        selectedDate={date}
        onKeyDown={handleTableKeyDown}
      />

      {isEditorOpen && (
        <TimeRecordEditor
          date={selectedDate}
          timeRecord={editingTimeRecord}
          isOpen={isEditorOpen}
          onClose={onEditorClose}
          onSave={saveTimeRecord}
          onDelete={deleteTimeRecord}
        />
      )}

      {/* Timer Creator Dialog */}
      {isTimerDialogOpen && (
        <TimerCreator
          isOpen={isTimerDialogOpen}
          onClose={() => setIsTimerDialogOpen(false)}
        />
      )}
    </div>
  )
}
