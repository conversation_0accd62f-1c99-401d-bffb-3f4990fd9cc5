import type { Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import { useEffect, useState } from 'react'
import { ConfirmationDialog } from '~/components/confirmation-dialog'
import { TimerCreator } from '~/components/timer-creator'
import { Button } from '~/components/ui/button'
import { canStopTimerFromDb, stopTimer } from '~/lib/model/app-state'
import { dbDeleteTimer } from '~/lib/services/database-service'
import styles from './timers.module.scss'

export const Route = createFileRoute('/timers/')({
  component: RouteComponent,
})

/**
 * Format a timestamp to a date string
 */
function formatDate(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleDateString()
}

/**
 * Format a timestamp to a time string
 */
function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

/**
 * Format a duration in milliseconds to a readable format
 */
function formatDuration(
  startTimestamp: number,
  endTimestamp: number | null
): string {
  if (!endTimestamp) return '--'

  const durationMs = endTimestamp - startTimestamp
  const seconds = Math.floor((durationMs / 1000) % 60)
  const minutes = Math.floor((durationMs / (1000 * 60)) % 60)
  const hours = Math.floor(durationMs / (1000 * 60 * 60))

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

function RouteComponent() {
  const z = useZero<Schema>()
  const [appState, appStateDetails] = useQuery(
    z.query.appState.where('id', '=', z.userID).related('runningTimer').one(),
    {
      ttl: 'forever',
    }
  )
  const runningTimer =
    appStateDetails.type === 'complete' ? appState?.runningTimer : undefined

  // State for delete confirmation dialog
  const [timerToDelete, setTimerToDelete] = useState<string | null>(null)
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // State for timer creation dialog
  const [isTimerDialogOpen, setIsTimerDialogOpen] = useState(false)

  // Fetch all timers
  const [timers] = useQuery(z.query.timers, {
    ttl: 'forever',
  })

  const canStopTimer = appState && canStopTimerFromDb(appState)

  // Handle keyboard shortcut (CMD+.) to open timer dialog
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if CMD+. (Mac) or CTRL+. (Windows/Linux) is pressed
      if ((e.metaKey || e.ctrlKey) && e.key === '.') {
        e.preventDefault()
        setIsTimerDialogOpen(true)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  /**
   * Start a timer
   */
  function startTimer(timerId: string) {
    z.mutate.timers
      .update({
        id: timerId,
        status: 'running',
        updatedAt: Date.now(),
        updatedBy: z.userID,
        startTimestamp: Date.now(),
      })
      .catch((error) => {
        console.error('Error starting timer:', error)
      })
  }

  // Handle delete button click
  const handleDeleteClick = (timerId: string) => {
    setTimerToDelete(timerId)
    setIsDeleteConfirmOpen(true)
  }

  // Handle confirmed deletion
  const handleConfirmDelete = () => {
    if (timerToDelete) {
      z.mutateBatch(async (tx) => {
        await dbDeleteTimer(z, tx, timerToDelete)
      }).then(() => {
        console.log('Timer deleted successfully')
        setIsDeleteConfirmOpen(false)
        setTimerToDelete(null)
      })
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.headerSection}>
        <h1>Timers</h1>
        <Button
          onClick={() => setIsTimerDialogOpen(true)}
          className={styles.createButton}
        >
          Create New Timer (CMD+.)
        </Button>
      </div>

      {/* Timer Creation Dialog */}
      <TimerCreator
        isOpen={isTimerDialogOpen}
        onClose={() => setIsTimerDialogOpen(false)}
      />
      {/* <TimerLauncher onClosed={() => setIsTimerDialogOpen(false)} /> */}

      <table className={styles.timersTable}>
        <thead>
          <tr>
            <th>Start Time</th>
            <th>End Time</th>
            <th>Duration</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {timers.length > 0 ? (
            timers.map((timer) => (
              <tr
                key={timer.id}
                className={
                  timer.id === runningTimer?.id ? styles.runningTimer : ''
                }
              >
                <td>
                  {formatDate(timer.startTimestamp)}{' '}
                  {formatTime(timer.startTimestamp)}
                </td>
                <td>
                  {timer.endTimestamp
                    ? `${formatDate(timer.endTimestamp)} ${formatTime(
                        timer.endTimestamp
                      )}`
                    : '--'}
                </td>
                <td>
                  {formatDuration(timer.startTimestamp, timer.endTimestamp)}
                </td>
                <td>
                  <span
                    className={
                      timer.status === 'running'
                        ? styles.statusRunning
                        : styles.statusStopped
                    }
                  >
                    {timer.status.charAt(0).toUpperCase() +
                      timer.status.slice(1)}
                  </span>
                </td>
                <td>
                  <div className={styles.actionButtons}>
                    {timer.status === 'stopped' && (
                      <button
                        className={styles.startButton}
                        onClick={() => startTimer(timer.id)}
                        type="button"
                      >
                        Start
                      </button>
                    )}
                    {timer.id === runningTimer?.id && (
                      <button
                        className={styles.stopButton}
                        onClick={() => stopTimer(z, timer.id, Date.now())}
                        disabled={!canStopTimer}
                        type="button"
                      >
                        Stop
                      </button>
                    )}
                    <button
                      className={styles.deleteButton}
                      onClick={() => handleDeleteClick(timer.id)}
                      type="button"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={5}>
                No timers found. Create a new timer to get started.
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleConfirmDelete}
        title="Confirm Deletion"
        description="Are you sure you want to delete this timer? This action cannot be undone."
        confirmText="Delete"
      />
    </div>
  )
}
