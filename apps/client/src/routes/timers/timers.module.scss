.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.headerSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.headerControls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.formItem {
  margin: 0;
}

.taskSelect {
  min-width: 250px;
}

.createButton {
  padding: 8px 16px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;

  &:hover {
    background-color: #45a049;
  }
}

.timersTable {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;

  th,
  td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }

  th {
    background-color: #f5f5f5;
    font-weight: bold;
  }

  tr:hover {
    background-color: #f9f9f9;
  }
}

.startButton {
  padding: 6px 12px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #45a049;
  }
}

.stopButton {
  padding: 6px 12px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background-color: #d32f2f;
  }
}

.statusRunning {
  color: #4caf50;
  font-weight: bold;
}

.statusStopped {
  color: #f44336;
}

.deleteButton {
  padding: 6px 12px;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-left: 8px;

  &:hover {
    background-color: #f57c00;
  }
}

.actionButtons {
  display: flex;
  align-items: center;
}

.runningTimer {
  background-color: yellow;
}
