import {
  Outlet,
  createFileRoute,
  redirect,
  useLocation,
  useNavigate,
} from '@tanstack/react-router'
import { type FC, useEffect, useRef } from 'react'

import { useAuth } from '../hooks/use-auth'
import { debugLog } from '../utils/log-utils'

const AuthLayout: FC = () => {
  const auth = useAuth()
  const navigate = useNavigate()
  const pathname = useLocation({
    select: (location) => location.pathname,
  })
  const pathRef = useRef(pathname)
  pathRef.current = pathname

  const authenticated = auth.isAuthenticated
  useEffect(() => {
    if (authenticated === 'unauthenticated') {
      const redirectUrl = pathRef.current
      debugLog('redirecting to login page with path', redirectUrl)
      navigate({
        to: '/login',
        search: {
          redirect: redirectUrl,
        },
      }).catch(console.error)
    }
  }, [authenticated, navigate])

  return (
    <>
      <Outlet />
    </>
  )
}

export const Route = createFileRoute('/_auth')({
  component: AuthLayout,
  beforeLoad: ({ context }) => {
    if (context.auth?.isAuthenticated === 'unauthenticated') {
      // eslint-disable-next-line @typescript-eslint/only-throw-error
      throw redirect({
        to: '/login',
        search: {
          redirect: location.pathname,
        },
      })
    }
  },
})
