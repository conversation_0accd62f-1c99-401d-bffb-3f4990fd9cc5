import { type Schema, schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import { invoke } from '@tauri-apps/api/core'
import Cookies from 'js-cookie'
import { useState } from 'react'
import { IdleTimeDisplay } from '~/components/IdleTimeDisplay'
import { Button } from '~/components/ui/button'
import { clientId } from '~/modules/state/active-client'
import { calculateServerUrl } from '~/modules/urls/url-builder'

export const Route = createFileRoute('/')({
  component: App,
})

function App() {
  const [greetMsg, setGreetMsg] = useState('')
  const [name, setName] = useState('')
  const [error] = useState('N/A')

  const z = useZero<Schema>()

  const toggleLogin = async () => {
    if (z.userID === 'anon') {
      try {
        // Use credentials: 'include' to ensure cookies are sent and received
        const response = await fetch(calculateServerUrl('/api/login'), {
          credentials: 'include',
          mode: 'cors',
        })
        console.log('Login response status:', response.status)

        if (response.ok) {
          console.log('Login successful')
          console.log('Current cookies after login:', document.cookie)
          debugHeaders(response.headers)

          const jwtData = await response.json()
          console.log('JWT verification data:', jwtData)

          // If we got the JWT from the server, set it manually in js-cookie and localStorage
          if (jwtData.jwt) {
            Cookies.set('jwt', jwtData.jwt)
            localStorage.setItem('jwt', jwtData.jwt)
            console.log('Manually set JWT cookie and localStorage')

            // Reload the page to apply the new JWT
            window.location.reload()
          } else {
            console.error('Failed to verify JWT:', response.status)
          }
        }
      } catch (error) {
        console.error('Error during login:', error)
      }
    } else {
      // Logout
      Cookies.remove('jwt')
      localStorage.removeItem('jwt')
      console.log('Removed JWT cookie and localStorage')
      window.location.reload()
    }
  }

  const [users] = useQuery(z.query.users, {
    ttl: 'forever',
  })
  const [appState] = useQuery(
    z.query.appState.where('id', '=', z.userID).one(),
    {
      ttl: 'forever',
    }
  )

  async function greet() {
    // Learn more about Tauri commands at https://v1.tauri.app/v1/guides/features/command
    setGreetMsg(await invoke('greet', { name }))
  }

  const inspect = async () => {
    alert('Open dev tools console tab to view inspector output.')
    const inspector = await z.inspect()
    const client = inspector.client

    const style =
      'background-color: darkblue; color: white; font-style: italic; font-size: 2em;'
    console.log('%cPrinting inspector output...', style)
    console.log(
      "%cTo see pretty tables, leave devtools open, then press 'Inspect' button in main UI again.",
      style
    )
    console.log(
      '%cSorry this is so ghetto I was too tired to make a debug dialog.',
      style
    )

    console.log('client:')
    console.log(client)
    console.log('client group:')
    console.log(client.clientGroup)
    console.log('client map:')
    console.log(await client.map())
    for (const tableName of Object.keys(schema.tables)) {
      console.log(`table ${tableName}:`)
      console.table(await client.rows(tableName))
    }
    console.log('client queries:')
    console.table(await client.queries())
    console.log('client group queries:')
    console.table(await client.clientGroup.queries())
    console.log('all clients in group')
    console.table(await client.clientGroup.clients())
  }

  return (
    <main className="container">
      <pre>
        Client ID {clientId} (
        {appState?.activeClientId === clientId ? 'active' : 'inactive'})
        {JSON.stringify(appState, null, 2)}
      </pre>
      <h1>Error</h1>
      <p>{error ?? 'N/A'}</p>
      <Button onClick={toggleLogin} type="button">
        Toggle Login
      </Button>
      <h2>Users ({z.userID})</h2>
      <ul>
        {users.map((user) => (
          <li key={user.id}>
            {user.name} ({user.id.slice(0, 8)})
          </li>
        ))}
      </ul>
      <form
        className="row"
        onSubmit={(e) => {
          e.preventDefault()
          greet()
        }}
      >
        <input
          id="greet-input"
          onChange={(e) => setName(e.currentTarget.value)}
          placeholder="Enter a name..."
        />
        <button type="submit">Greet</button>
      </form>
      <p>{greetMsg}</p>

      <button type="button" onMouseDown={() => inspect()}>
        Inspect
      </button>

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-4">Idle Time API Test</h2>
        <IdleTimeDisplay />
      </div>
    </main>
  )
}

function debugHeaders(headers: Headers) {
  for (const [key, value] of headers.entries()) {
    console.log(`${key}: ${value}`)
  }
}
