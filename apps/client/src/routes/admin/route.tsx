import { Link, Outlet, createFileRoute } from '@tanstack/react-router'
import { FileJson, Home, Inbox } from 'lucide-react'
import type { FC } from 'react'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from '~/components/ui/sidebar'

export const Route = createFileRoute('/admin')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <div>
      <SidebarProvider>
        <AppSidebar />
        <SidebarTrigger />

        <Outlet />
      </SidebarProvider>
    </div>
  )
}

const items = [
  {
    title: 'Accounts',
    url: '/admin/accounts',
    icon: Home,
  },
  {
    title: 'Remote Services',
    url: '/admin/remote-service',
    icon: Inbox,
  },
  {
    title: 'Import',
    url: '/admin/import',
    icon: <PERSON><PERSON>son,
  },
  {
    title: 'Project Catalogs',
    url: '/admin/projectcatalogs',
    icon: FileJson,
  },
  {
    title: 'Task Catalogs',
    url: '/admin/taskcatalogs',
    icon: FileJson,
  },
  {
    title: 'Customers',
    url: '/admin/customers',
    icon: FileJson,
  },
  {
    title: 'Projects',
    url: '/admin/projects',
    icon: FileJson,
  },
  {
    title: 'Tasks',
    url: '/admin/tasks',
    icon: FileJson,
  },
]

const AppSidebar: FC = () => {
  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Admin</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link to={item.url}>
                      <>
                        <item.icon />
                        <span>{item.title}</span>
                      </>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
