import type { Customer, Project, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useDebounce } from '@uidotdev/usehooks'
import { type RefObject, useEffect, useMemo, useRef, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { DataTable, type DataTableRef } from '~/components/datatable'
import { ProjectEditor } from '~/components/project-editor'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { createGlobalKeyboardHandler } from '~/lib/utils/keyboard'

interface ProjectTableProps {
  projects: Project[]
  selectedProjectId: string | null
  onRowClick: (projectId: string) => void
  onRowDoubleClick?: (projectId: string) => void
  tableRef?: RefObject<DataTableRef | null>
}

// Define search schema for URL parameters
const searchSchema = v.object({
  customerId: v.optional(v.string()),
})

export const Route = createFileRoute('/admin/projects')({
  component: RouteComponent,
  validateSearch: searchSchema,
})

// Utility functions
function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

// Define columns for the DataTable
function createColumns(customers: Customer[]): ColumnDef<Project>[] {
  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'customerId',
      header: 'Customer',
      cell: ({ row }) => {
        const customer = customers.find((c) => c.id === row.original.customerId)
        return customer?.name || '-'
      },
    },
    {
      accessorKey: 'color',
      header: 'Color',
      cell: ({ row }) => {
        const color = row.original.color
        return color ? (
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: color }}
            />
            {color}
          </div>
        ) : (
          '-'
        )
      },
    },
    {
      accessorKey: 'timeNormalizationType',
      header: 'Time Normalization',
      cell: ({ row }) => row.original.timeNormalizationType || '-',
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated',
      cell: ({ row }) => formatDate(row.original.updatedAt),
    },
  ]
}

// Project table component
function ProjectTable({
  projects,
  selectedProjectId,
  onRowClick,
  onRowDoubleClick,
  tableRef,
}: ProjectTableProps) {
  const z = useZero<Schema>()

  // Fetch all customers to display customer names
  const [customers = []] = useQuery(z.query.customers, {
    ttl: 'forever',
  })

  const columns = useMemo(() => createColumns(customers), [customers])

  return (
    <div className="rounded-md border">
      <DataTable
        ref={tableRef}
        columns={columns}
        data={projects}
        onRowClick={(project: Project) => onRowClick(project.id)}
        onRowDoubleClick={
          onRowDoubleClick
            ? (project: Project) => onRowDoubleClick(project.id)
            : undefined
        }
        getRowId={(project: Project) => project.id}
        selectedRowId={selectedProjectId || undefined}
      />
    </div>
  )
}

// Customer filter component
interface CustomerFilterProps {
  customers: Customer[]
  selectedCustomerId: string | undefined
  onCustomerChange: (customerId: string | undefined) => void
}

function CustomerFilter({
  customers,
  selectedCustomerId,
  onCustomerChange,
}: CustomerFilterProps) {
  // Use "all" as a special value for showing all customers
  const selectValue = selectedCustomerId || 'all'

  return (
    <div className="flex items-center gap-4 mb-4">
      <div className="flex items-center gap-2">
        <label htmlFor="customer-filter" className="font-medium text-sm">
          Filter by customer:
        </label>
        <Select
          value={selectValue}
          onValueChange={(value) =>
            onCustomerChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[200px]" id="customer-filter">
            <SelectValue placeholder="All customers" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All customers</SelectItem>
            {customers.map((customer) => (
              <SelectItem key={customer.id} value={customer.id}>
                {customer.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {selectedCustomerId && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onCustomerChange(undefined)}
        >
          Clear Filter
        </Button>
      )}
    </div>
  )
}

function RouteComponent() {
  const z = useZero<Schema>()
  const { customerId } = Route.useSearch()
  const navigate = Route.useNavigate()
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(
    null
  )
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const tableRef = useRef<DataTableRef>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Create a query with search and customer filter if needed
  const projectsQuery = useMemo(() => {
    let query = z.query.projects

    // Apply filters
    if (debouncedSearchQuery.length >= 2 || customerId) {
      query = query.where(({ cmp, and }) => {
        const conditions = []

        // Add name search condition if search query is provided
        if (debouncedSearchQuery.length >= 2) {
          const searchPattern = `%${debouncedSearchQuery}%`
          conditions.push(cmp('name', 'ILIKE', searchPattern))
        }

        // Add customer filter condition if customerId is provided
        if (customerId) {
          conditions.push(cmp('customerId', '=', customerId))
        }

        // Combine conditions with AND if both filters are applied, otherwise just return the single condition
        return conditions.length > 1 ? and(...conditions) : conditions[0]
      })
    }

    // Default sorting
    query = query.orderBy('name', 'asc')

    return query
  }, [z, debouncedSearchQuery, customerId])

  // Fetch projects with the query
  const [projects = []] = useQuery(projectsQuery, {
    ttl: debouncedSearchQuery.length >= 2 ? 0 : 'forever', // Don't cache search results
  })

  // Fetch all customers for the filter
  const [customers = []] = useQuery(z.query.customers, {
    ttl: 'forever',
  })

  // Get the selected project
  const selectedProject =
    projects.find((project) => project.id === selectedProjectId) || null

  // Handle customer filter change
  const handleCustomerChange = (newCustomerId: string | undefined) => {
    navigate({
      search: (prev) => ({
        ...prev,
        customerId: newCustomerId,
      }),
    })
  }

  // Handle row click (select)
  const handleRowClick = (projectId: string) => {
    setSelectedProjectId(projectId)
    navigate({ to: '/admin/tasks', search: { projectId } })
  }

  // Handle row double click (edit)
  const handleRowDoubleClick = (projectId: string) => {
    setSelectedProjectId(projectId)
    setIsEditorOpen(true)
  }

  // Handle save
  const handleSave = async (project: Project) => {
    try {
      if (projects.some((p) => p.id === project.id)) {
        // Update existing project
        await z.mutate.projects.update({
          id: project.id,
          name: project.name,
          customerId: project.customerId,
          color: project.color,
          timeNormalizationType: project.timeNormalizationType,
          timeNormalizationConfig: project.timeNormalizationConfig,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new project
        await z.mutate.projects.insert({
          ...project,
          id: project.id || uuidv7(),
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving project:', error)
    }
  }

  // Handle delete
  const handleDelete = async (project: Project) => {
    try {
      await z.mutate.projects.delete({
        id: project.id,
      })
    } catch (error) {
      console.error('Error deleting project:', error)
    }
  }

  // Add global keyboard event listener to focus the table when Escape is pressed
  useEffect(() => {
    // Create a handler that only executes if no input element is focused
    const handleGlobalKeyDown = createGlobalKeyboardHandler((e) => {
      // Focus the table when Escape is pressed
      if (e.key === 'Escape' && !isEditorOpen) {
        e.preventDefault()
        tableRef.current?.focus()
      }
    })

    window.addEventListener('keydown', handleGlobalKeyDown)
    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown)
    }
  }, [isEditorOpen])

  // Add new project
  const handleAddNew = () => {
    setSelectedProjectId(null)
    setIsEditorOpen(true)
  }

  // Edit selected project
  const handleEditSelected = () => {
    if (selectedProjectId) {
      setIsEditorOpen(true)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Projects</h1>
        <div className="flex gap-2">
          {selectedProjectId && (
            <Button onClick={handleEditSelected}>Edit Selected</Button>
          )}
          <Button onClick={handleAddNew}>Add New</Button>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <CustomerFilter
          customers={customers}
          selectedCustomerId={customerId}
          onCustomerChange={handleCustomerChange}
        />

        <div className="flex-1 flex items-center">
          <Input
            placeholder="Search projects by name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
          {searchQuery && (
            <Button
              variant="ghost"
              onClick={() => setSearchQuery('')}
              className="ml-2"
            >
              Clear
            </Button>
          )}
        </div>
      </div>

      <ProjectTable
        projects={projects}
        selectedProjectId={selectedProjectId}
        onRowClick={handleRowClick}
        onRowDoubleClick={handleRowDoubleClick}
        tableRef={tableRef}
      />

      {isEditorOpen && (
        <ProjectEditor
          project={selectedProject}
          isOpen={isEditorOpen}
          onClose={() => setIsEditorOpen(false)}
          onSave={handleSave}
          onDelete={handleDelete}
        />
      )}
    </div>
  )
}
