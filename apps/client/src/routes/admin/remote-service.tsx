import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAppForm } from '@/components/ui/tanstack-form'
import type { RemoteService, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { useStore } from '@tanstack/react-form'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useEffect, useRef, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { url, email, minLength, optional, pipe, string } from 'valibot'
import { ConfirmationDialog } from '~/components/confirmation-dialog'
import { DataTable, type DataTableRef } from '~/components/datatable'
import { ServiceTypeOptions } from '~/components/model/service-types'
import { createGlobalKeyboardHandler } from '~/lib/utils/keyboard'

import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'

// Define Valibot schema for remote service validation
const RemoteServiceSchema = v.object({
  id: string(),
  name: pipe(string(), minLength(2, 'Name must be at least 2 characters')),
  serviceType: pipe(string(), minLength(1, 'Service type is required')),
  remoteUrl: pipe(string(), url('Please enter a valid URL')),
  remoteUser: optional(
    pipe(string(), email('Please enter a valid email address')),
    ''
  ),
  remotePassword: string(),
})

type RemoteServiceInput = v.InferInput<typeof RemoteServiceSchema>

export const Route = createFileRoute('/admin/remote-service')({
  component: RouteComponent,
})

function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

function RemoteServiceEditor({
  remoteService,
  isOpen,
  onClose,
  onSave,
  onDelete,
}: {
  remoteService: RemoteService | null
  isOpen: boolean
  onClose: () => void
  onSave: (remoteService: RemoteService) => void
  onDelete?: (remoteService: RemoteService) => void
}) {
  const defaultValues: RemoteServiceInput = {
    id: remoteService?.id || '',
    name: remoteService?.name || '',
    serviceType: remoteService?.serviceType || '',
    remoteUrl: remoteService?.remoteUrl || '',
    remoteUser: remoteService?.remoteUser || '',
    remotePassword: remoteService?.remotePassword || '',
  }
  const form = useAppForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const updatedService: RemoteService = {
        ...remoteService,
        id: value.id ?? uuidv7(),
        name: value.name,
        serviceType: value.serviceType,
        remoteUrl: value.remoteUrl,
        remoteUser: value.remoteUser,
        remotePassword: value.remotePassword,
        updatedAt: Date.now(),
      } as RemoteService

      onSave(updatedService)
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: RemoteServiceSchema,
    },
  })

  // Reset form when remote service changes
  useEffect(() => {
    if (isOpen) {
      if (remoteService) {
        form.reset({
          id: remoteService.id,
          name: remoteService.name,
          serviceType: remoteService.serviceType,
          remoteUrl: remoteService.remoteUrl || '',
          remoteUser: remoteService.remoteUser || '',
          remotePassword: remoteService.remotePassword || '',
        })
      } else {
        form.reset({
          id: '',
          name: '',
          serviceType: '',
          remoteUrl: '',
          remoteUser: '',
          remotePassword: '',
        })
      }
    }
  }, [remoteService, isOpen, form])

  const { errors } = useStore(form.store, (state) => ({
    errors: state.errors,
  }))

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true)
  }

  // Handle confirmed deletion
  const handleConfirmDelete = () => {
    if (remoteService && onDelete) {
      onDelete(remoteService)
      setIsDeleteConfirmOpen(false)
    }
  }

  console.log('form-errors', errors)
  if (!isOpen) return null

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => !open && onClose()}
        modal={true}
      >
        <DialogContent className="sm:max-w-[500px] max-h-svh overflow-y-scroll">
          <form.AppForm>
            <form
              onSubmit={(e) => {
                e.preventDefault()
                e.stopPropagation()
                void form.handleSubmit()
              }}
            >
              <DialogHeader>
                <DialogTitle>
                  {remoteService ? 'Edit' : 'Add'} Remote Service
                </DialogTitle>
                <DialogDescription>
                  Configure connection details for remote services like Jira.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4 ">
                <form.AppField name="name">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Name</field.FormLabel>
                      <field.FormControl>
                        <Input
                          placeholder="Name"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                        />
                      </field.FormControl>
                      <field.FormDescription>
                        The display name of the remote service.
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>

                <form.AppField name="serviceType">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Service Type</field.FormLabel>
                      <field.FormControl>
                        <Select
                          defaultValue={field.state.value}
                          onValueChange={(e) => field.handleChange(e)}
                        >
                          <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="Service Type" />
                          </SelectTrigger>
                          <SelectContent>
                            {ServiceTypeOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </field.FormControl>
                      <field.FormDescription>
                        The display name of the remote service.
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>

                <form.AppField name="remoteUrl">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Remote URL</field.FormLabel>
                      <field.FormControl>
                        <Input
                          type="url"
                          placeholder="URL"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                        />
                      </field.FormControl>
                      <field.FormDescription>
                        The server url of the remote service.
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>

                <form.AppField name="remoteUser">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Username</field.FormLabel>
                      <field.FormControl>
                        <Input
                          placeholder="Username"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                        />
                      </field.FormControl>
                      <field.FormDescription>
                        The user name of the remote service.
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>

                <form.AppField name="remotePassword">
                  {(field) => (
                    <field.FormItem>
                      <field.FormLabel>Password</field.FormLabel>
                      <field.FormControl>
                        <Input
                          type="password"
                          placeholder="Password"
                          value={field.state.value}
                          onChange={(e) => field.handleChange(e.target.value)}
                          onBlur={field.handleBlur}
                        />
                      </field.FormControl>
                      <field.FormDescription>
                        The password of the remote service.
                      </field.FormDescription>
                      <field.FormMessage />
                    </field.FormItem>
                  )}
                </form.AppField>
              </div>

              <DialogFooter>
                <div className="flex w-full justify-between">
                  <div>
                    {remoteService && onDelete && (
                      <Button
                        type="button"
                        variant="destructive"
                        onClick={handleDeleteClick}
                      >
                        Delete
                      </Button>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button type="button" variant="outline" onClick={onClose}>
                      Cancel
                    </Button>
                    <form.Subscribe
                      selector={(state) => [
                        state.canSubmit,
                        state.isSubmitting,
                      ]}
                    >
                      {([canSubmit, isSubmitting]) => (
                        <Button
                          type="submit"
                          disabled={!canSubmit || isSubmitting}
                        >
                          {isSubmitting
                            ? 'Saving...'
                            : remoteService
                              ? 'Update'
                              : 'Create'}
                        </Button>
                      )}
                    </form.Subscribe>
                  </div>
                </div>
              </DialogFooter>
            </form>
          </form.AppForm>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleConfirmDelete}
        title="Confirm Deletion"
        description="Are you sure you want to delete this remote service? This action cannot be undone."
        confirmText="Delete"
      />
    </>
  )
}

// Define columns for the DataTable
function createColumns(): ColumnDef<RemoteService>[] {
  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'serviceType',
      header: 'Service Type',
    },
    {
      accessorKey: 'remoteUrl',
      header: 'URL',
    },
    {
      accessorKey: 'remoteUser',
      header: 'Username',
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated',
      cell: ({ row }) => formatDate(row.original.updatedAt),
    },
  ]
}

function RouteComponent() {
  const z = useZero<Schema>()
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(
    null
  )
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const tableRef = useRef<DataTableRef>(null)
  const navigate = Route.useNavigate()

  // Focus the table when the component mounts
  useEffect(() => {
    // Small delay to ensure the table is fully rendered
    const timeoutId = setTimeout(() => {
      if (tableRef.current) {
        tableRef.current.focus()
      }
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [])

  // Add global keyboard event listener to focus the table when Escape is pressed
  useEffect(() => {
    // Create a handler that only executes if no input element is focused
    const handleGlobalKeyDown = createGlobalKeyboardHandler(
      (e: KeyboardEvent) => {
        // Focus the table when Escape is pressed
        if (e.key === 'Escape' && !isEditorOpen) {
          e.preventDefault()
          tableRef.current?.focus()
        }
      }
    )

    window.addEventListener('keydown', handleGlobalKeyDown)
    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown)
    }
  }, [isEditorOpen])

  // We don't need a separate keyboard handler anymore as it's handled by the DataTable component

  // Fetch all remote services
  const [remoteServices = []] = useQuery(z.query.remoteServices, {
    ttl: 'forever',
  })

  // Get the selected remote service
  const selectedService =
    remoteServices.find((service) => service.id === selectedServiceId) || null

  // Handle row single click (select and navigate to project catalogs)
  const handleRowClick = (serviceId: string) => {
    setSelectedServiceId(serviceId)
    // Navigate to project catalogs with this remote service selected
    navigate({
      to: '/admin/projectcatalogs',
      search: {
        remoteServiceId: serviceId,
      },
    })
  }

  // Handle row double click (open editor)
  const handleRowDoubleClick = (serviceId: string) => {
    setSelectedServiceId(serviceId)
    setIsEditorOpen(true)
  }

  // Handle save
  const handleSave = async (remoteService: RemoteService) => {
    try {
      if (remoteServices.some((s) => s.id === remoteService.id)) {
        // Update existing service
        await z.mutate.remoteServices.update({
          id: remoteService.id,
          name: remoteService.name,
          serviceType: remoteService.serviceType,
          remoteUrl: remoteService.remoteUrl,
          remoteUser: remoteService.remoteUser,
          remotePassword: remoteService.remotePassword,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new service
        await z.mutate.remoteServices.insert({
          ...remoteService,
          id: remoteService.id || uuidv7(),
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving remote service:', error)
    }
  }

  // Handle delete
  const handleDelete = async (remoteService: RemoteService) => {
    try {
      await z.mutate.remoteServices.delete({
        id: remoteService.id,
      })
      setIsEditorOpen(false)
    } catch (error) {
      console.error('Error deleting remote service:', error)
    }
  }

  // Add new remote service
  const handleAddNew = () => {
    setSelectedServiceId(null)
    setIsEditorOpen(true)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Remote Services</h1>
        <Button onClick={handleAddNew}>Add New</Button>
      </div>

      <div className="rounded-md border">
        <DataTable
          ref={tableRef}
          columns={createColumns()}
          data={remoteServices}
          onRowClick={(service) => handleRowClick(service.id)}
          onRowDoubleClick={(service) => handleRowDoubleClick(service.id)}
          getRowId={(service) => service.id}
          selectedRowId={selectedServiceId || undefined}
        />
      </div>

      <RemoteServiceEditor
        remoteService={selectedService}
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSave}
        onDelete={handleDelete}
      />
    </div>
  )
}
