import type { Customer, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useDebounce } from '@uidotdev/usehooks'
import {
  type FC,
  type RefObject,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { uuidv7 } from 'uuidv7'
import { CustomerEditor } from '~/components/customer-editor'
import { DataTable, type DataTableRef } from '~/components/datatable'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { createGlobalKeyboardHandler } from '~/lib/utils/keyboard'

interface CustomerTableProps {
  customers: Customer[]
  selectedCustomerId: string | null
  onRowClick: (customerId: string) => void
  onRowDoubleClick?: (customerId: string) => void
  tableRef?: RefObject<DataTableRef | null>
}

export const Route = createFileRoute('/admin/customers')({
  component: RouteComponent,
})

// Utility functions
function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

// Define columns for the DataTable
function createColumns(): ColumnDef<Customer>[] {
  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'rateValue',
      header: 'Rate',
    },
    {
      accessorKey: 'rateCurrency',
      header: 'Currency',
    },
    {
      accessorKey: 'timeNormalizationType',
      header: 'Time Normalization',
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => formatDate(row.original.createdAt),
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated',
      cell: ({ row }) => formatDate(row.original.updatedAt),
    },
  ]
}

// Customer table component
const CustomerTable: FC<CustomerTableProps> = ({
  customers,
  selectedCustomerId,
  onRowClick,
  onRowDoubleClick,
  tableRef,
}) => {
  const columns = useMemo(() => createColumns(), [])

  return (
    <div className="rounded-md border">
      <DataTable
        ref={tableRef}
        columns={columns}
        data={customers}
        onRowClick={(customer: Customer) => onRowClick(customer.id)}
        onRowDoubleClick={
          onRowDoubleClick
            ? (customer: Customer) => onRowDoubleClick(customer.id)
            : undefined
        }
        getRowId={(customer: Customer) => customer.id}
        selectedRowId={selectedCustomerId || undefined}
      />
    </div>
  )
}

function RouteComponent() {
  const z = useZero<Schema>()
  const navigate = Route.useNavigate()
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    null
  )
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const tableRef = useRef<DataTableRef>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Create a query with search filter if needed
  const customersQuery = useMemo(() => {
    let query = z.query.customers

    if (debouncedSearchQuery.length >= 2) {
      // Use ILIKE for case-insensitive search with pattern matching
      const searchPattern = `%${debouncedSearchQuery}%`
      query = query.where(({ cmp }) => cmp('name', 'ILIKE', searchPattern))
    } else {
      // Default sorting
      query = query.orderBy('name', 'asc')
    }

    return query
  }, [z, debouncedSearchQuery])

  // Fetch customers with the query
  const [customers = []] = useQuery(customersQuery, {
    ttl: debouncedSearchQuery.length >= 2 ? 0 : 'forever', // Don't cache search results
  })

  // Add global keyboard event listener to focus the table when Escape is pressed
  useEffect(() => {
    // Create a handler that only executes if no input element is focused
    const handleGlobalKeyDown = createGlobalKeyboardHandler((e) => {
      // Focus the table when Escape is pressed
      if (e.key === 'Escape' && !isEditorOpen) {
        e.preventDefault()
        tableRef.current?.focus()
      }
    })

    window.addEventListener('keydown', handleGlobalKeyDown)
    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown)
    }
  }, [isEditorOpen])

  const selectedCustomer =
    customers.find((customer) => customer.id === selectedCustomerId) || null

  const handleRowClick = (customerId: string) => {
    setSelectedCustomerId(customerId)
    navigate({ to: '/admin/projects', search: { customerId } })
  }

  // Handle row double click (edit)
  const handleRowDoubleClick = (customerId: string) => {
    setSelectedCustomerId(customerId)
    setIsEditorOpen(true)
  }

  // Handle save
  const handleSave = async (customer: Customer) => {
    try {
      if (customers.some((c) => c.id === customer.id)) {
        // Update existing customer
        await z.mutate.customers.update({
          id: customer.id,
          name: customer.name,
          rateValue: customer.rateValue,
          rateCurrency: customer.rateCurrency,
          timeNormalizationType: customer.timeNormalizationType,
          timeNormalizationConfig: customer.timeNormalizationConfig,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new customer
        await z.mutate.customers.insert({
          ...customer,
          id: customer.id || uuidv7(),
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving customer:', error)
    }
  }

  // Handle delete
  const handleDelete = async (customer: Customer) => {
    try {
      await z.mutate.customers.delete({
        id: customer.id,
      })
    } catch (error) {
      console.error('Error deleting customer:', error)
    }
  }

  // Add new customer
  const handleAddNew = () => {
    setSelectedCustomerId(null)
    setIsEditorOpen(true)
  }

  // Edit selected customer
  const handleEditSelected = () => {
    if (selectedCustomerId) {
      setIsEditorOpen(true)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Customers</h1>
        <div className="flex gap-2">
          {selectedCustomerId && (
            <Button onClick={handleEditSelected}>Edit Selected</Button>
          )}
          <Button onClick={handleAddNew}>Add New</Button>
        </div>
      </div>

      <div className="flex items-center mb-4">
        <Input
          placeholder="Search customers by name..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
        {searchQuery && (
          <Button
            variant="ghost"
            onClick={() => setSearchQuery('')}
            className="ml-2"
          >
            Clear
          </Button>
        )}
      </div>

      <CustomerTable
        customers={customers}
        selectedCustomerId={selectedCustomerId}
        onRowClick={handleRowClick}
        onRowDoubleClick={handleRowDoubleClick}
        tableRef={tableRef}
      />

      {isEditorOpen && (
        <CustomerEditor
          customer={selectedCustomer}
          isOpen={isEditorOpen}
          onClose={() => setIsEditorOpen(false)}
          onSave={handleSave}
          onDelete={handleDelete}
        />
      )}
    </div>
  )
}
