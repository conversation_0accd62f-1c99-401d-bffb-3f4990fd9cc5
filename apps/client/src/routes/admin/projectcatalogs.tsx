import { useAppForm } from '@/components/ui/tanstack-form'
import type { ProjectCatalog, RemoteService, Schema } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import { useDebounce } from '@uidotdev/usehooks'
import { Search as SearchIcon } from 'lucide-react'
import { useEffect, useMemo, useRef, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { minLength, optional, pipe, string } from 'valibot'
import { ConfirmationDialog } from '~/components/confirmation-dialog'
import { DataTable, type DataTableRef } from '~/components/datatable'
import { RemoteServicePicker } from '~/components/remote-service-picker'
import { Button } from '~/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog'
import { Input } from '~/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'
import { createGlobalKeyboardHandler } from '~/lib/utils/keyboard'

// Interface definitions
interface ProjectCatalogEditorProps {
  projectCatalog: ProjectCatalog | null
  isOpen: boolean
  onClose: () => void
  onSave: (projectCatalog: ProjectCatalog) => void
  onDelete?: (projectCatalog: ProjectCatalog) => void
}

// Filter component for remote service and search
interface FilterBarProps {
  remoteServices: RemoteService[]
  selectedRemoteServiceId: string | undefined
  onRemoteServiceChange: (remoteServiceId: string | undefined) => void
  searchQuery: string
  onSearchChange: (value: string) => void
}

// Define Valibot schema for project catalog validation
const ProjectCatalogSchema = v.object({
  id: string(),
  name: pipe(string(), minLength(2, 'Name must be at least 2 characters')),
  key: optional(string(), ''),
  remoteId: optional(string(), ''),
  remoteUrl: optional(string(), ''),
  remoteServiceId: optional(string(), ''),
})

type ProjectCatalogInput = v.InferInput<typeof ProjectCatalogSchema>

// Define search schema for URL parameters
const searchSchema = v.object({
  remoteServiceId: v.optional(v.string()),
  search: v.optional(v.string()),
})

export const Route = createFileRoute('/admin/projectcatalogs')({
  component: RouteComponent,
  validateSearch: searchSchema,
})

// Utility functions
function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

function FilterBar({
  remoteServices,
  selectedRemoteServiceId,
  onRemoteServiceChange,
  searchQuery,
  onSearchChange,
}: FilterBarProps) {
  // Use "all" as a special value for showing all remote services
  const selectValue = selectedRemoteServiceId || 'all'

  return (
    <div className="flex flex-wrap items-center gap-4 mb-4">
      <div className="flex items-center gap-2">
        <label htmlFor="remote-service-filter" className="font-medium text-sm">
          Filter by remote service:
        </label>
        <Select
          value={selectValue}
          onValueChange={(value) =>
            onRemoteServiceChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[200px]" id="remote-service-filter">
            <SelectValue placeholder="All remote services" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All remote services</SelectItem>
            {remoteServices.map((service) => (
              <SelectItem key={service.id} value={service.id}>
                {service.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center gap-2">
        <label htmlFor="search-filter" className="font-medium text-sm">
          Search by name:
        </label>
        <div className="relative">
          <SearchIcon className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="search-filter"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            placeholder="Search project catalogs..."
            className="pl-8 w-[250px]"
          />
        </div>
      </div>

      {(selectedRemoteServiceId || searchQuery) && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => {
            if (selectedRemoteServiceId) onRemoteServiceChange(undefined)
            if (searchQuery) onSearchChange('')
          }}
        >
          Clear Filters
        </Button>
      )}
    </div>
  )
}

function ProjectCatalogEditor({
  projectCatalog,
  isOpen,
  onClose,
  onSave,
  onDelete,
}: ProjectCatalogEditorProps) {
  const defaultValues: ProjectCatalogInput = {
    id: projectCatalog?.id || '',
    name: projectCatalog?.name || '',
    key: projectCatalog?.key || '',
    remoteId: projectCatalog?.remoteId || '',
    remoteUrl: projectCatalog?.remoteUrl || '',
    remoteServiceId: projectCatalog?.remoteServiceId || '',
  }

  const form = useAppForm({
    defaultValues,
    onSubmit: async ({ value }) => {
      const updatedProjectCatalog: ProjectCatalog = {
        ...projectCatalog,
        id: value.id ?? uuidv7(),
        name: value.name,
        key: value.key,
        remoteId: value.remoteId,
        remoteUrl: value.remoteUrl,
        remoteServiceId: value.remoteServiceId,
        updatedAt: Date.now(),
      } as ProjectCatalog

      onSave(updatedProjectCatalog)
      onClose()
    },
    // Use form-level validation with Valibot schema
    validators: {
      onChange: ProjectCatalogSchema,
    },
  })

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false)

  // Handle delete button click
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true)
  }

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (projectCatalog && onDelete) {
      onDelete(projectCatalog)
    }
    setIsDeleteConfirmOpen(false)
    onClose()
  }

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => !open && onClose()}
        modal={true}
      >
        <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
          <form
            onSubmit={(e) => {
              e.preventDefault()
              e.stopPropagation()
              form.handleSubmit()
            }}
          >
            <DialogHeader>
              <DialogTitle>
                {projectCatalog ? 'Edit' : 'Add'} Project Catalog
              </DialogTitle>
              <DialogDescription>
                Configure project catalog details for remote integration.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-3 py-3">
              <form.AppField name="name">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Name</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Project Catalog Name"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Project catalog name
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="key">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Key</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Project Key"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Project key (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="remoteServiceId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Remote Service</field.FormLabel>
                    <field.FormControl>
                      <RemoteServicePicker
                        value={field.state.value}
                        onChange={(value) => field.handleChange(value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      Remote service this project catalog belongs to (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="remoteId">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Remote ID</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Remote ID"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      ID in the remote system (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>

              <form.AppField name="remoteUrl">
                {(field) => (
                  <field.FormItem>
                    <field.FormLabel>Remote URL</field.FormLabel>
                    <field.FormControl>
                      <Input
                        placeholder="Remote URL"
                        value={field.state.value}
                        onChange={(e) => field.handleChange(e.target.value)}
                        onBlur={field.handleBlur}
                      />
                    </field.FormControl>
                    <field.FormDescription>
                      URL in the remote system (optional)
                    </field.FormDescription>
                    <field.FormMessage />
                  </field.FormItem>
                )}
              </form.AppField>
            </div>
            <DialogFooter className="gap-2 mt-4">
              {projectCatalog && onDelete && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={handleDeleteClick}
                >
                  Delete
                </Button>
              )}
              <div className="flex-1" />
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">Save</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        isOpen={isDeleteConfirmOpen}
        onOpenChange={setIsDeleteConfirmOpen}
        onConfirm={handleDeleteConfirm}
        title="Delete Project Catalog"
        description="Are you sure you want to delete this project catalog? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
    </>
  )
}

// Define columns for the DataTable
function createColumns(): ColumnDef<
  ProjectCatalog & { remoteService?: RemoteService }
>[] {
  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'key',
      header: 'Key',
    },
    {
      accessorKey: 'remoteService.name',
      header: 'Remote Service',
      cell: ({ row }) => {
        const data = row.original as ProjectCatalog & {
          remoteService?: RemoteService
        }
        return data.remoteService?.name || '-'
      },
    },
    {
      accessorKey: 'remoteId',
      header: 'Remote ID',
      cell: ({ row }) => {
        const data = row.original as ProjectCatalog
        return data.remoteId || '-'
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({ row }) => {
        const data = row.original as ProjectCatalog
        return formatDate(data.createdAt)
      },
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated',
      cell: ({ row }) => {
        const data = row.original as ProjectCatalog
        return formatDate(data.updatedAt)
      },
    },
  ]
}

function RouteComponent() {
  const z = useZero<Schema>()
  const { remoteServiceId } = Route.useSearch()
  const navigate = Route.useNavigate()
  const [selectedProjectCatalogId, setSelectedProjectCatalogId] = useState<
    string | null
  >(null)
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const tableRef = useRef<DataTableRef>(null)
  const columns = useMemo(() => createColumns(), [])

  // State for search query
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Focus the table when the component mounts
  useEffect(() => {
    // Small delay to ensure the table is fully rendered
    const timeoutId = setTimeout(() => {
      if (tableRef.current) {
        tableRef.current.focus()
      }
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [])

  // Create a query that includes filtering by remoteServiceId and search if provided
  const projectCatalogsQuery = useMemo(() => {
    let query = z.query.projectCatalogs.related('remoteService')

    if (remoteServiceId) {
      query = query.where('remoteServiceId', '=', remoteServiceId)
    }

    // Add search filter if search query is at least 2 characters
    if (debouncedSearchQuery && debouncedSearchQuery.length >= 2) {
      const searchPattern = `%${debouncedSearchQuery}%`
      query = query.where(({ cmp }) => cmp('name', 'ILIKE', searchPattern))
    }

    return query.orderBy('name', 'asc')
  }, [z, remoteServiceId, debouncedSearchQuery])

  // Fetch project catalogs with related remote service data
  const [projectCatalogs = []] = useQuery(projectCatalogsQuery, {
    ttl: 'forever',
  })

  // Fetch all remote services for the filter dropdown
  const [remoteServices = []] = useQuery(z.query.remoteServices, {
    ttl: 'forever',
  })

  // Get the selected project catalog
  const selectedProjectCatalog =
    projectCatalogs.find(
      (catalog) => catalog.id === selectedProjectCatalogId
    ) || null

  // Handle remote service filter change
  const handleRemoteServiceChange = (
    newRemoteServiceId: string | undefined
  ) => {
    navigate({
      search: (prev) => ({
        ...prev,
        remoteServiceId: newRemoteServiceId,
      }),
    })
  }

  // Handle row single click (select and navigate to task catalogs)
  const handleRowClick = (projectCatalogId: string) => {
    setSelectedProjectCatalogId(projectCatalogId)
    // Navigate to task catalogs with this project catalog selected
    navigate({
      to: '/admin/taskcatalogs',
      search: {
        projectCatalogId: projectCatalogId,
      },
    })
  }

  // Handle row double click (open editor)
  const handleRowDoubleClick = (projectCatalogId: string) => {
    setSelectedProjectCatalogId(projectCatalogId)
    setIsEditorOpen(true)
  }

  // Handle save
  const handleSave = async (projectCatalog: ProjectCatalog) => {
    try {
      if (projectCatalogs.some((p) => p.id === projectCatalog.id)) {
        // Update existing project catalog
        await z.mutate.projectCatalogs.update({
          id: projectCatalog.id,
          name: projectCatalog.name,
          key: projectCatalog.key,
          remoteId: projectCatalog.remoteId,
          remoteUrl: projectCatalog.remoteUrl,
          remoteServiceId: projectCatalog.remoteServiceId,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new project catalog
        await z.mutate.projectCatalogs.insert({
          ...projectCatalog,
          id: projectCatalog.id || uuidv7(),
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving project catalog:', error)
    }
  }

  // Handle delete
  const handleDelete = async (projectCatalog: ProjectCatalog) => {
    try {
      await z.mutate.projectCatalogs.delete({
        id: projectCatalog.id,
      })
    } catch (error) {
      console.error('Error deleting project catalog:', error)
    }
  }

  // Add global keyboard event listener to focus the table when Escape is pressed
  useEffect(() => {
    // Create a handler that only executes if no input element is focused
    const handleGlobalKeyDown = createGlobalKeyboardHandler<KeyboardEvent>(
      (e) => {
        // Focus the table when Escape is pressed
        if (e.key === 'Escape' && !isEditorOpen) {
          e.preventDefault()
          tableRef.current?.focus()
        }
      }
    )

    window.addEventListener('keydown', handleGlobalKeyDown)
    return () => {
      window.removeEventListener('keydown', handleGlobalKeyDown)
    }
  }, [isEditorOpen])

  // Add new project catalog
  const handleAddNew = () => {
    setSelectedProjectCatalogId(null)
    setIsEditorOpen(true)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Project Catalogs</h1>
        <Button onClick={handleAddNew}>Add New</Button>
      </div>

      <FilterBar
        remoteServices={remoteServices}
        selectedRemoteServiceId={remoteServiceId}
        onRemoteServiceChange={handleRemoteServiceChange}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
      />

      <div className="rounded-md border">
        <DataTable
          ref={tableRef}
          columns={columns}
          data={projectCatalogs}
          onRowClick={(
            projectCatalog: ProjectCatalog & { remoteService?: RemoteService }
          ) => handleRowClick(projectCatalog.id)}
          onRowDoubleClick={(
            projectCatalog: ProjectCatalog & { remoteService?: RemoteService }
          ) => handleRowDoubleClick(projectCatalog.id)}
          getRowId={(
            projectCatalog: ProjectCatalog & { remoteService?: RemoteService }
          ) => projectCatalog.id}
          selectedRowId={selectedProjectCatalogId || undefined}
        />
      </div>

      <ProjectCatalogEditor
        projectCatalog={selectedProjectCatalog}
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSave}
        onDelete={handleDelete}
      />
    </div>
  )
}
