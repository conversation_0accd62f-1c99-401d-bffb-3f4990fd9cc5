import type { Project<PERSON>atalog, Schema, TaskCatalog } from '@ftt/shared'
import { useQuery, useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import type { ColumnDef } from '@tanstack/react-table'
import type { Row } from '@tanstack/react-table'
import type { KeyboardEvent } from 'react'
import { useMemo, useRef, useState } from 'react'
import { uuidv7 } from 'uuidv7'
import * as v from 'valibot'
import { DataTable, type DataTableRef } from '~/components/datatable'
import { TaskCatalogEditor } from '~/components/task-catalog-editor'
import { Button } from '~/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select'

// Interface definitions
interface TaskCatalogTableProps {
  taskCatalogs: Array<TaskCatalog & { projectCatalog?: ProjectCatalog }>
  selectedTaskCatalogId: string | null
  onRowClick: (taskCatalogId: string) => void
  onRowDoubleClick: (taskCatalogId: string) => void
  onKeyDown: (e: KeyboardEvent<HTMLTableElement>) => void
}

// Project catalog filter component
interface ProjectCatalogFilterProps {
  projectCatalogs: ProjectCatalog[]
  selectedProjectCatalogId: string | undefined
  onProjectCatalogChange: (projectCatalogId: string | undefined) => void
}

// Define search schema for URL parameters
const searchSchema = v.object({
  projectCatalogId: v.optional(v.string()),
})

export const Route = createFileRoute('/admin/taskcatalogs/')({
  component: RouteComponent,
  validateSearch: searchSchema,
})

// Utility functions
function formatDate(timestamp: number | null | undefined): string {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

// Define columns for the DataTable
function createColumns(): ColumnDef<
  TaskCatalog & { projectCatalog?: ProjectCatalog }
>[] {
  return [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'key',
      header: 'Key',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return data.key || '-'
      },
    },
    {
      accessorKey: 'projectCatalog.name',
      header: 'Project Catalog',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return data.projectCatalog?.name || '-'
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return data.status || '-'
      },
    },
    {
      accessorKey: 'remoteId',
      header: 'Remote ID',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return data.remoteId || '-'
      },
    },
    {
      accessorKey: 'lastUsed',
      header: 'Last Used',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return formatDate(data.lastUsed)
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'Created',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return formatDate(data.createdAt)
      },
    },
    {
      accessorKey: 'updatedAt',
      header: 'Updated',
      cell: ({
        row,
      }: { row: Row<TaskCatalog & { projectCatalog?: ProjectCatalog }> }) => {
        const data = row.original
        return formatDate(data.updatedAt)
      },
    },
  ]
}

// Task catalog table component
function TaskCatalogTable({
  taskCatalogs,
  selectedTaskCatalogId,
  onRowClick,
  onRowDoubleClick,
}: TaskCatalogTableProps) {
  const tableRef = useRef<DataTableRef>(null)
  const columns = useMemo(() => createColumns(), [])

  return (
    <div className="rounded-md border">
      <DataTable
        ref={tableRef}
        columns={columns}
        data={taskCatalogs}
        onRowClick={(
          taskCatalog: TaskCatalog & { projectCatalog?: ProjectCatalog }
        ) => onRowClick(taskCatalog.id)}
        onRowDoubleClick={(
          taskCatalog: TaskCatalog & { projectCatalog?: ProjectCatalog }
        ) => onRowDoubleClick(taskCatalog.id)}
        getRowId={(
          taskCatalog: TaskCatalog & { projectCatalog?: ProjectCatalog }
        ) => taskCatalog.id}
        selectedRowId={selectedTaskCatalogId || undefined}
      />
    </div>
  )
}

function ProjectCatalogFilter({
  projectCatalogs,
  selectedProjectCatalogId,
  onProjectCatalogChange,
}: ProjectCatalogFilterProps) {
  // Use "all" as a special value for showing all project catalogs
  const selectValue = selectedProjectCatalogId || 'all'

  return (
    <div className="flex items-center gap-4 mb-4">
      <div className="flex items-center gap-2">
        <label htmlFor="project-catalog-filter" className="font-medium text-sm">
          Filter by project catalog:
        </label>
        <Select
          value={selectValue}
          onValueChange={(value) =>
            onProjectCatalogChange(value === 'all' ? undefined : value)
          }
        >
          <SelectTrigger className="w-[250px]" id="project-catalog-filter">
            <SelectValue placeholder="All project catalogs" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All project catalogs</SelectItem>
            {projectCatalogs.map((catalog) => (
              <SelectItem key={catalog.id} value={catalog.id}>
                {catalog.name} {catalog.key ? `(${catalog.key})` : ''}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {selectedProjectCatalogId && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onProjectCatalogChange(undefined)}
        >
          Clear Filter
        </Button>
      )}
    </div>
  )
}

function RouteComponent() {
  const z = useZero<Schema>()
  const { projectCatalogId } = Route.useSearch()
  const navigate = Route.useNavigate()
  const [selectedTaskCatalogId, setSelectedTaskCatalogId] = useState<
    string | null
  >(null)
  const [isEditorOpen, setIsEditorOpen] = useState(false)

  // Create a query that includes filtering by projectCatalogId if provided
  const taskCatalogsQuery = useMemo(() => {
    let query = z.query.taskCatalogs.related('projectCatalog')

    if (projectCatalogId) {
      query = query.where('projectCatalogId', '=', projectCatalogId)
    }

    // Order by pinned first, then by last used (with nulls last), then by name
    return query
      .orderBy('pinned', 'desc')
      .orderBy('lastUsed', 'desc')
      .orderBy('name', 'asc')
  }, [z, projectCatalogId])

  // Fetch task catalogs with related project catalog data
  const [taskCatalogs = []] = useQuery(taskCatalogsQuery, {
    ttl: 'forever',
  })

  // Fetch all project catalogs for the filter dropdown
  const [projectCatalogs = []] = useQuery(
    z.query.projectCatalogs.orderBy('name', 'asc'),
    {
      ttl: 'forever',
    }
  )

  // Get the selected task catalog
  const selectedTaskCatalog =
    taskCatalogs.find((catalog) => catalog.id === selectedTaskCatalogId) || null

  // Handle project catalog filter change
  const handleProjectCatalogChange = (
    newProjectCatalogId: string | undefined
  ) => {
    navigate({
      search: (prev) => ({
        ...prev,
        projectCatalogId: newProjectCatalogId,
      }),
    })
  }

  // Handle row click
  const handleRowClick = (taskCatalogId: string) => {
    navigate({ to: `/admin/taskcatalogs/${taskCatalogId}` })
  }

  // Handle row double click
  const handleRowDoubleClick = (taskCatalogId: string) => {
    setSelectedTaskCatalogId(taskCatalogId)
    setIsEditorOpen(true)
  }

  // Handle save
  const handleSave = async (taskCatalog: TaskCatalog) => {
    try {
      if (taskCatalogs.some((p) => p.id === taskCatalog.id)) {
        // Update existing task catalog
        await z.mutate.taskCatalogs.update({
          id: taskCatalog.id,
          name: taskCatalog.name,
          key: taskCatalog.key,
          status: taskCatalog.status,
          remoteId: taskCatalog.remoteId,
          remoteUrl: taskCatalog.remoteUrl,
          projectCatalogId: taskCatalog.projectCatalogId,
          pinned: taskCatalog.pinned,
          updatedAt: Date.now(),
          updatedBy: z.userID,
        })
      } else {
        // Create new task catalog
        await z.mutate.taskCatalogs.insert({
          ...taskCatalog,
          id: taskCatalog.id || uuidv7(),
          lastUsed: null,
          createdAt: Date.now(),
          updatedAt: Date.now(),
          createdBy: z.userID,
          updatedBy: z.userID,
        })
      }
    } catch (error) {
      console.error('Error saving task catalog:', error)
    }
  }

  // Handle delete
  const handleDelete = async (taskCatalog: TaskCatalog) => {
    try {
      await z.mutate.taskCatalogs.delete({
        id: taskCatalog.id,
      })
    } catch (error) {
      console.error('Error deleting task catalog:', error)
    }
  }

  // Add new task catalog
  const handleAddNew = () => {
    setSelectedTaskCatalogId(null)
    setIsEditorOpen(true)
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Task Catalogs</h1>
        <Button onClick={handleAddNew}>Add New</Button>
      </div>

      <ProjectCatalogFilter
        projectCatalogs={projectCatalogs}
        selectedProjectCatalogId={projectCatalogId}
        onProjectCatalogChange={handleProjectCatalogChange}
      />

      <TaskCatalogTable
        taskCatalogs={taskCatalogs}
        selectedTaskCatalogId={selectedTaskCatalogId}
        onRowClick={handleRowClick}
        onRowDoubleClick={handleRowDoubleClick}
        onKeyDown={() => {}}
      />

      <TaskCatalogEditor
        taskCatalog={selectedTaskCatalog}
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSave}
        onDelete={handleDelete}
      />
    </div>
  )
}
