import type { Schema } from '@ftt/shared'
import { useZero } from '@rocicorp/zero/react'
import { createFileRoute } from '@tanstack/react-router'
import { invariant } from 'es-toolkit'
import { AlertCircle, CheckCircle, FileJson, Upload } from 'lucide-react'
import { type ChangeEvent, useRef, useState } from 'react'
import { toast } from 'sonner'
import { CustomerPicker } from '~/components/customer-picker'
import { RemoteServicePicker } from '~/components/remote-service-picker'
import { Alert, AlertDescription, AlertTitle } from '~/components/ui/alert'
import { Button } from '~/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import {
  type ImportJson,
  ImportJsonSchema,
  importProjectsAndTasks,
} from '~/modules/admin/import/import-schema'

export const Route = createFileRoute('/admin/import')({
  component: ImportComponent,
})

function ImportComponent() {
  const z = useZero<Schema>()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [file, setFile] = useState<File | null>(null)
  const [jsonData, setJsonData] = useState<ImportJson | null>(null)
  const [remoteServiceId, setRemoteServiceId] = useState<string | undefined>(
    undefined
  )
  const [customerId, setCustomerId] = useState<string | undefined>(undefined)
  const [parseError, setParseError] = useState<string | null>(null)
  const [isImporting, setIsImporting] = useState(false)
  const [importResult, setImportResult] = useState<{
    projectCatalogsCreated: number
    projectCatalogsUpdated: number
    taskCatalogsCreated: number
    taskCatalogsUpdated: number
    errors: string[]
  } | null>(null)

  // Handle file selection
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null
    setFile(selectedFile)
    setJsonData(null)
    setParseError(null)
    setImportResult(null)

    if (selectedFile) {
      const reader = new FileReader()
      reader.onload = (event) => {
        try {
          const content = event.target?.result as string
          const parsedData = JSON.parse(content)

          // Validate with Zod schema
          const validationResult = ImportJsonSchema.safeParse(parsedData)

          if (validationResult.success) {
            setJsonData(validationResult.data)
            setParseError(null)
          } else {
            console.error('Validation errors:', validationResult.error)
            setParseError(
              'Invalid JSON format. Please check the console for details.'
            )
            setJsonData(null)
          }
        } catch (error) {
          console.error('Error parsing JSON:', error)
          setParseError(
            'Error parsing JSON file. Please check the file format.'
          )
          setJsonData(null)
        }
      }
      reader.readAsText(selectedFile)
    }
  }

  // Handle import button click
  const handleImport = async () => {
    if (!jsonData) return

    setIsImporting(true)
    try {
      invariant(customerId, 'Customer is required')
      invariant(remoteServiceId, 'Remote service is required')
      const result = await importProjectsAndTasks(
        z,
        customerId,
        remoteServiceId,
        jsonData
      )
      setImportResult(result)

      if (result.errors.length === 0) {
        toast.success('Import completed successfully')
      } else {
        toast.warning('Import completed with some errors')
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error('Import failed. Please check the console for details.')
    } finally {
      setIsImporting(false)
    }
  }

  // Handle file selection button click
  const handleSelectFile = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-6 p-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Import Projects & Tasks</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Import JSON Data</CardTitle>
          <CardDescription>
            Upload a JSON file to import project catalogs and task catalogs.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <RemoteServicePicker
              value={remoteServiceId}
              className="min-w-40"
              onChange={(value) => setRemoteServiceId(value)}
              display="combobox"
              autoFocus
            />
            <CustomerPicker
              value={customerId}
              className="min-w-40"
              onChange={(value) => setCustomerId(value)}
              display="combobox"
              autoFocus
            />
            <Input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleFileChange}
              className="hidden"
            />
            <Button
              onClick={handleSelectFile}
              variant="outline"
              className="flex gap-2"
            >
              <FileJson className="h-4 w-4" />
              Select JSON File
            </Button>
            {file && (
              <span className="text-sm text-muted-foreground">
                {file.name} ({Math.round(file.size / 1024)} KB)
              </span>
            )}
          </div>

          {parseError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{parseError}</AlertDescription>
            </Alert>
          )}

          {jsonData && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Valid JSON</AlertTitle>
              <AlertDescription>
                Found {jsonData.content.length} customers with{' '}
                {jsonData.content.reduce(
                  (acc, customer) => acc + customer.projects.length,
                  0
                )}{' '}
                projects and{' '}
                {jsonData.content.reduce(
                  (acc, customer) =>
                    acc +
                    customer.projects.reduce(
                      (acc2, project) => acc2 + project.tasks.length,
                      0
                    ),
                  0
                )}{' '}
                tasks.
              </AlertDescription>
            </Alert>
          )}

          {importResult && (
            <Alert
              variant={
                importResult.errors.length > 0 ? 'destructive' : 'default'
              }
            >
              <AlertTitle>Import Result</AlertTitle>
              <AlertDescription>
                <div>
                  Created {importResult.projectCatalogsCreated} project catalogs
                </div>
                <div>
                  Updated {importResult.projectCatalogsUpdated} project catalogs
                </div>
                <div>
                  Created {importResult.taskCatalogsCreated} task catalogs
                </div>
                <div>
                  Updated {importResult.taskCatalogsUpdated} task catalogs
                </div>
                {importResult.errors.length > 0 && (
                  <div className="mt-2">
                    <div className="font-semibold">
                      Errors ({importResult.errors.length}):
                    </div>
                    <ul className="list-disc pl-5 text-sm max-h-40 overflow-y-auto">
                      {importResult.errors.map((error, index) => (
                        <li key={`error-${error.toString()}-${index}`}>
                          {error}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter>
          <Button
            onClick={handleImport}
            disabled={!jsonData || isImporting}
            className="flex gap-2"
          >
            {isImporting ? (
              <>Processing...</>
            ) : (
              <>
                <Upload className="h-4 w-4" />
                Import Data
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
