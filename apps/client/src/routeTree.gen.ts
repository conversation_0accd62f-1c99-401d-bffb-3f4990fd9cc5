/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginImport } from './routes/login'
import { Route as AuthImport } from './routes/_auth'
import { Route as ReportsRouteImport } from './routes/reports/route'
import { Route as AdminRouteImport } from './routes/admin/route'
import { Route as IndexImport } from './routes/index'
import { Route as TimersIndexImport } from './routes/timers/index'
import { Route as TimeRecordsIndexImport } from './routes/time-records/index'
import { Route as ReportsIndexImport } from './routes/reports/index'
import { Route as AdminIndexImport } from './routes/admin/index'
import { Route as ReportsTimerecordDetailsImport } from './routes/reports/timerecord-details'
import { Route as ReportsTimerecordAggregateImport } from './routes/reports/timerecord-aggregate'
import { Route as AdminUsersImport } from './routes/admin/users'
import { Route as AdminRemoteServiceImport } from './routes/admin/remote-service'
import { Route as AdminProjectsImport } from './routes/admin/projects'
import { Route as AdminProjectcatalogsImport } from './routes/admin/projectcatalogs'
import { Route as AdminImportImport } from './routes/admin/import'
import { Route as AdminCustomersImport } from './routes/admin/customers'
import { Route as AdminAccountsImport } from './routes/admin/accounts'
import { Route as AdminTasksIndexImport } from './routes/admin/tasks/index'
import { Route as AdminTaskcatalogsIndexImport } from './routes/admin/taskcatalogs/index'
import { Route as AdminTasksTaskIdImport } from './routes/admin/tasks/$taskId'
import { Route as AdminTaskcatalogsTaskCatalogIdImport } from './routes/admin/taskcatalogs/$taskCatalogId'

// Create/Update Routes

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const ReportsRouteRoute = ReportsRouteImport.update({
  id: '/reports',
  path: '/reports',
  getParentRoute: () => rootRoute,
} as any)

const AdminRouteRoute = AdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

const TimersIndexRoute = TimersIndexImport.update({
  id: '/timers/',
  path: '/timers/',
  getParentRoute: () => rootRoute,
} as any)

const TimeRecordsIndexRoute = TimeRecordsIndexImport.update({
  id: '/time-records/',
  path: '/time-records/',
  getParentRoute: () => rootRoute,
} as any)

const ReportsIndexRoute = ReportsIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => ReportsRouteRoute,
} as any)

const AdminIndexRoute = AdminIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AdminRouteRoute,
} as any)

const ReportsTimerecordDetailsRoute = ReportsTimerecordDetailsImport.update({
  id: '/timerecord-details',
  path: '/timerecord-details',
  getParentRoute: () => ReportsRouteRoute,
} as any)

const ReportsTimerecordAggregateRoute = ReportsTimerecordAggregateImport.update(
  {
    id: '/timerecord-aggregate',
    path: '/timerecord-aggregate',
    getParentRoute: () => ReportsRouteRoute,
  } as any,
)

const AdminUsersRoute = AdminUsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminRemoteServiceRoute = AdminRemoteServiceImport.update({
  id: '/remote-service',
  path: '/remote-service',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminProjectsRoute = AdminProjectsImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminProjectcatalogsRoute = AdminProjectcatalogsImport.update({
  id: '/projectcatalogs',
  path: '/projectcatalogs',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminImportRoute = AdminImportImport.update({
  id: '/import',
  path: '/import',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminCustomersRoute = AdminCustomersImport.update({
  id: '/customers',
  path: '/customers',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminAccountsRoute = AdminAccountsImport.update({
  id: '/accounts',
  path: '/accounts',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminTasksIndexRoute = AdminTasksIndexImport.update({
  id: '/tasks/',
  path: '/tasks/',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminTaskcatalogsIndexRoute = AdminTaskcatalogsIndexImport.update({
  id: '/taskcatalogs/',
  path: '/taskcatalogs/',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminTasksTaskIdRoute = AdminTasksTaskIdImport.update({
  id: '/tasks/$taskId',
  path: '/tasks/$taskId',
  getParentRoute: () => AdminRouteRoute,
} as any)

const AdminTaskcatalogsTaskCatalogIdRoute =
  AdminTaskcatalogsTaskCatalogIdImport.update({
    id: '/taskcatalogs/$taskCatalogId',
    path: '/taskcatalogs/$taskCatalogId',
    getParentRoute: () => AdminRouteRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminRouteImport
      parentRoute: typeof rootRoute
    }
    '/reports': {
      id: '/reports'
      path: '/reports'
      fullPath: '/reports'
      preLoaderRoute: typeof ReportsRouteImport
      parentRoute: typeof rootRoute
    }
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/admin/accounts': {
      id: '/admin/accounts'
      path: '/accounts'
      fullPath: '/admin/accounts'
      preLoaderRoute: typeof AdminAccountsImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/customers': {
      id: '/admin/customers'
      path: '/customers'
      fullPath: '/admin/customers'
      preLoaderRoute: typeof AdminCustomersImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/import': {
      id: '/admin/import'
      path: '/import'
      fullPath: '/admin/import'
      preLoaderRoute: typeof AdminImportImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/projectcatalogs': {
      id: '/admin/projectcatalogs'
      path: '/projectcatalogs'
      fullPath: '/admin/projectcatalogs'
      preLoaderRoute: typeof AdminProjectcatalogsImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/projects': {
      id: '/admin/projects'
      path: '/projects'
      fullPath: '/admin/projects'
      preLoaderRoute: typeof AdminProjectsImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/remote-service': {
      id: '/admin/remote-service'
      path: '/remote-service'
      fullPath: '/admin/remote-service'
      preLoaderRoute: typeof AdminRemoteServiceImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/users': {
      id: '/admin/users'
      path: '/users'
      fullPath: '/admin/users'
      preLoaderRoute: typeof AdminUsersImport
      parentRoute: typeof AdminRouteImport
    }
    '/reports/timerecord-aggregate': {
      id: '/reports/timerecord-aggregate'
      path: '/timerecord-aggregate'
      fullPath: '/reports/timerecord-aggregate'
      preLoaderRoute: typeof ReportsTimerecordAggregateImport
      parentRoute: typeof ReportsRouteImport
    }
    '/reports/timerecord-details': {
      id: '/reports/timerecord-details'
      path: '/timerecord-details'
      fullPath: '/reports/timerecord-details'
      preLoaderRoute: typeof ReportsTimerecordDetailsImport
      parentRoute: typeof ReportsRouteImport
    }
    '/admin/': {
      id: '/admin/'
      path: '/'
      fullPath: '/admin/'
      preLoaderRoute: typeof AdminIndexImport
      parentRoute: typeof AdminRouteImport
    }
    '/reports/': {
      id: '/reports/'
      path: '/'
      fullPath: '/reports/'
      preLoaderRoute: typeof ReportsIndexImport
      parentRoute: typeof ReportsRouteImport
    }
    '/time-records/': {
      id: '/time-records/'
      path: '/time-records'
      fullPath: '/time-records'
      preLoaderRoute: typeof TimeRecordsIndexImport
      parentRoute: typeof rootRoute
    }
    '/timers/': {
      id: '/timers/'
      path: '/timers'
      fullPath: '/timers'
      preLoaderRoute: typeof TimersIndexImport
      parentRoute: typeof rootRoute
    }
    '/admin/taskcatalogs/$taskCatalogId': {
      id: '/admin/taskcatalogs/$taskCatalogId'
      path: '/taskcatalogs/$taskCatalogId'
      fullPath: '/admin/taskcatalogs/$taskCatalogId'
      preLoaderRoute: typeof AdminTaskcatalogsTaskCatalogIdImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/tasks/$taskId': {
      id: '/admin/tasks/$taskId'
      path: '/tasks/$taskId'
      fullPath: '/admin/tasks/$taskId'
      preLoaderRoute: typeof AdminTasksTaskIdImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/taskcatalogs/': {
      id: '/admin/taskcatalogs/'
      path: '/taskcatalogs'
      fullPath: '/admin/taskcatalogs'
      preLoaderRoute: typeof AdminTaskcatalogsIndexImport
      parentRoute: typeof AdminRouteImport
    }
    '/admin/tasks/': {
      id: '/admin/tasks/'
      path: '/tasks'
      fullPath: '/admin/tasks'
      preLoaderRoute: typeof AdminTasksIndexImport
      parentRoute: typeof AdminRouteImport
    }
  }
}

// Create and export the route tree

interface AdminRouteRouteChildren {
  AdminAccountsRoute: typeof AdminAccountsRoute
  AdminCustomersRoute: typeof AdminCustomersRoute
  AdminImportRoute: typeof AdminImportRoute
  AdminProjectcatalogsRoute: typeof AdminProjectcatalogsRoute
  AdminProjectsRoute: typeof AdminProjectsRoute
  AdminRemoteServiceRoute: typeof AdminRemoteServiceRoute
  AdminUsersRoute: typeof AdminUsersRoute
  AdminIndexRoute: typeof AdminIndexRoute
  AdminTaskcatalogsTaskCatalogIdRoute: typeof AdminTaskcatalogsTaskCatalogIdRoute
  AdminTasksTaskIdRoute: typeof AdminTasksTaskIdRoute
  AdminTaskcatalogsIndexRoute: typeof AdminTaskcatalogsIndexRoute
  AdminTasksIndexRoute: typeof AdminTasksIndexRoute
}

const AdminRouteRouteChildren: AdminRouteRouteChildren = {
  AdminAccountsRoute: AdminAccountsRoute,
  AdminCustomersRoute: AdminCustomersRoute,
  AdminImportRoute: AdminImportRoute,
  AdminProjectcatalogsRoute: AdminProjectcatalogsRoute,
  AdminProjectsRoute: AdminProjectsRoute,
  AdminRemoteServiceRoute: AdminRemoteServiceRoute,
  AdminUsersRoute: AdminUsersRoute,
  AdminIndexRoute: AdminIndexRoute,
  AdminTaskcatalogsTaskCatalogIdRoute: AdminTaskcatalogsTaskCatalogIdRoute,
  AdminTasksTaskIdRoute: AdminTasksTaskIdRoute,
  AdminTaskcatalogsIndexRoute: AdminTaskcatalogsIndexRoute,
  AdminTasksIndexRoute: AdminTasksIndexRoute,
}

const AdminRouteRouteWithChildren = AdminRouteRoute._addFileChildren(
  AdminRouteRouteChildren,
)

interface ReportsRouteRouteChildren {
  ReportsTimerecordAggregateRoute: typeof ReportsTimerecordAggregateRoute
  ReportsTimerecordDetailsRoute: typeof ReportsTimerecordDetailsRoute
  ReportsIndexRoute: typeof ReportsIndexRoute
}

const ReportsRouteRouteChildren: ReportsRouteRouteChildren = {
  ReportsTimerecordAggregateRoute: ReportsTimerecordAggregateRoute,
  ReportsTimerecordDetailsRoute: ReportsTimerecordDetailsRoute,
  ReportsIndexRoute: ReportsIndexRoute,
}

const ReportsRouteRouteWithChildren = ReportsRouteRoute._addFileChildren(
  ReportsRouteRouteChildren,
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteRouteWithChildren
  '/reports': typeof ReportsRouteRouteWithChildren
  '': typeof AuthRoute
  '/login': typeof LoginRoute
  '/admin/accounts': typeof AdminAccountsRoute
  '/admin/customers': typeof AdminCustomersRoute
  '/admin/import': typeof AdminImportRoute
  '/admin/projectcatalogs': typeof AdminProjectcatalogsRoute
  '/admin/projects': typeof AdminProjectsRoute
  '/admin/remote-service': typeof AdminRemoteServiceRoute
  '/admin/users': typeof AdminUsersRoute
  '/reports/timerecord-aggregate': typeof ReportsTimerecordAggregateRoute
  '/reports/timerecord-details': typeof ReportsTimerecordDetailsRoute
  '/admin/': typeof AdminIndexRoute
  '/reports/': typeof ReportsIndexRoute
  '/time-records': typeof TimeRecordsIndexRoute
  '/timers': typeof TimersIndexRoute
  '/admin/taskcatalogs/$taskCatalogId': typeof AdminTaskcatalogsTaskCatalogIdRoute
  '/admin/tasks/$taskId': typeof AdminTasksTaskIdRoute
  '/admin/taskcatalogs': typeof AdminTaskcatalogsIndexRoute
  '/admin/tasks': typeof AdminTasksIndexRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof AuthRoute
  '/login': typeof LoginRoute
  '/admin/accounts': typeof AdminAccountsRoute
  '/admin/customers': typeof AdminCustomersRoute
  '/admin/import': typeof AdminImportRoute
  '/admin/projectcatalogs': typeof AdminProjectcatalogsRoute
  '/admin/projects': typeof AdminProjectsRoute
  '/admin/remote-service': typeof AdminRemoteServiceRoute
  '/admin/users': typeof AdminUsersRoute
  '/reports/timerecord-aggregate': typeof ReportsTimerecordAggregateRoute
  '/reports/timerecord-details': typeof ReportsTimerecordDetailsRoute
  '/admin': typeof AdminIndexRoute
  '/reports': typeof ReportsIndexRoute
  '/time-records': typeof TimeRecordsIndexRoute
  '/timers': typeof TimersIndexRoute
  '/admin/taskcatalogs/$taskCatalogId': typeof AdminTaskcatalogsTaskCatalogIdRoute
  '/admin/tasks/$taskId': typeof AdminTasksTaskIdRoute
  '/admin/taskcatalogs': typeof AdminTaskcatalogsIndexRoute
  '/admin/tasks': typeof AdminTasksIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteRouteWithChildren
  '/reports': typeof ReportsRouteRouteWithChildren
  '/_auth': typeof AuthRoute
  '/login': typeof LoginRoute
  '/admin/accounts': typeof AdminAccountsRoute
  '/admin/customers': typeof AdminCustomersRoute
  '/admin/import': typeof AdminImportRoute
  '/admin/projectcatalogs': typeof AdminProjectcatalogsRoute
  '/admin/projects': typeof AdminProjectsRoute
  '/admin/remote-service': typeof AdminRemoteServiceRoute
  '/admin/users': typeof AdminUsersRoute
  '/reports/timerecord-aggregate': typeof ReportsTimerecordAggregateRoute
  '/reports/timerecord-details': typeof ReportsTimerecordDetailsRoute
  '/admin/': typeof AdminIndexRoute
  '/reports/': typeof ReportsIndexRoute
  '/time-records/': typeof TimeRecordsIndexRoute
  '/timers/': typeof TimersIndexRoute
  '/admin/taskcatalogs/$taskCatalogId': typeof AdminTaskcatalogsTaskCatalogIdRoute
  '/admin/tasks/$taskId': typeof AdminTasksTaskIdRoute
  '/admin/taskcatalogs/': typeof AdminTaskcatalogsIndexRoute
  '/admin/tasks/': typeof AdminTasksIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/reports'
    | ''
    | '/login'
    | '/admin/accounts'
    | '/admin/customers'
    | '/admin/import'
    | '/admin/projectcatalogs'
    | '/admin/projects'
    | '/admin/remote-service'
    | '/admin/users'
    | '/reports/timerecord-aggregate'
    | '/reports/timerecord-details'
    | '/admin/'
    | '/reports/'
    | '/time-records'
    | '/timers'
    | '/admin/taskcatalogs/$taskCatalogId'
    | '/admin/tasks/$taskId'
    | '/admin/taskcatalogs'
    | '/admin/tasks'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/login'
    | '/admin/accounts'
    | '/admin/customers'
    | '/admin/import'
    | '/admin/projectcatalogs'
    | '/admin/projects'
    | '/admin/remote-service'
    | '/admin/users'
    | '/reports/timerecord-aggregate'
    | '/reports/timerecord-details'
    | '/admin'
    | '/reports'
    | '/time-records'
    | '/timers'
    | '/admin/taskcatalogs/$taskCatalogId'
    | '/admin/tasks/$taskId'
    | '/admin/taskcatalogs'
    | '/admin/tasks'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/reports'
    | '/_auth'
    | '/login'
    | '/admin/accounts'
    | '/admin/customers'
    | '/admin/import'
    | '/admin/projectcatalogs'
    | '/admin/projects'
    | '/admin/remote-service'
    | '/admin/users'
    | '/reports/timerecord-aggregate'
    | '/reports/timerecord-details'
    | '/admin/'
    | '/reports/'
    | '/time-records/'
    | '/timers/'
    | '/admin/taskcatalogs/$taskCatalogId'
    | '/admin/tasks/$taskId'
    | '/admin/taskcatalogs/'
    | '/admin/tasks/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRouteRoute: typeof AdminRouteRouteWithChildren
  ReportsRouteRoute: typeof ReportsRouteRouteWithChildren
  AuthRoute: typeof AuthRoute
  LoginRoute: typeof LoginRoute
  TimeRecordsIndexRoute: typeof TimeRecordsIndexRoute
  TimersIndexRoute: typeof TimersIndexRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRouteRoute: AdminRouteRouteWithChildren,
  ReportsRouteRoute: ReportsRouteRouteWithChildren,
  AuthRoute: AuthRoute,
  LoginRoute: LoginRoute,
  TimeRecordsIndexRoute: TimeRecordsIndexRoute,
  TimersIndexRoute: TimersIndexRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/admin",
        "/reports",
        "/_auth",
        "/login",
        "/time-records/",
        "/timers/"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/admin": {
      "filePath": "admin/route.tsx",
      "children": [
        "/admin/accounts",
        "/admin/customers",
        "/admin/import",
        "/admin/projectcatalogs",
        "/admin/projects",
        "/admin/remote-service",
        "/admin/users",
        "/admin/",
        "/admin/taskcatalogs/$taskCatalogId",
        "/admin/tasks/$taskId",
        "/admin/taskcatalogs/",
        "/admin/tasks/"
      ]
    },
    "/reports": {
      "filePath": "reports/route.tsx",
      "children": [
        "/reports/timerecord-aggregate",
        "/reports/timerecord-details",
        "/reports/"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx"
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/admin/accounts": {
      "filePath": "admin/accounts.tsx",
      "parent": "/admin"
    },
    "/admin/customers": {
      "filePath": "admin/customers.tsx",
      "parent": "/admin"
    },
    "/admin/import": {
      "filePath": "admin/import.tsx",
      "parent": "/admin"
    },
    "/admin/projectcatalogs": {
      "filePath": "admin/projectcatalogs.tsx",
      "parent": "/admin"
    },
    "/admin/projects": {
      "filePath": "admin/projects.tsx",
      "parent": "/admin"
    },
    "/admin/remote-service": {
      "filePath": "admin/remote-service.tsx",
      "parent": "/admin"
    },
    "/admin/users": {
      "filePath": "admin/users.tsx",
      "parent": "/admin"
    },
    "/reports/timerecord-aggregate": {
      "filePath": "reports/timerecord-aggregate.tsx",
      "parent": "/reports"
    },
    "/reports/timerecord-details": {
      "filePath": "reports/timerecord-details.tsx",
      "parent": "/reports"
    },
    "/admin/": {
      "filePath": "admin/index.tsx",
      "parent": "/admin"
    },
    "/reports/": {
      "filePath": "reports/index.tsx",
      "parent": "/reports"
    },
    "/time-records/": {
      "filePath": "time-records/index.tsx"
    },
    "/timers/": {
      "filePath": "timers/index.tsx"
    },
    "/admin/taskcatalogs/$taskCatalogId": {
      "filePath": "admin/taskcatalogs/$taskCatalogId.tsx",
      "parent": "/admin"
    },
    "/admin/tasks/$taskId": {
      "filePath": "admin/tasks/$taskId.tsx",
      "parent": "/admin"
    },
    "/admin/taskcatalogs/": {
      "filePath": "admin/taskcatalogs/index.tsx",
      "parent": "/admin"
    },
    "/admin/tasks/": {
      "filePath": "admin/tasks/index.tsx",
      "parent": "/admin"
    }
  }
}
ROUTE_MANIFEST_END */
