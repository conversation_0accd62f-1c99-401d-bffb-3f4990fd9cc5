import React, { type FC, type PropsWithChildren, useContext } from 'react'

import { debugLog } from '../utils/log-utils'

export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated'

export interface SAuthContext {
  isAuthenticated: AuthStatus
  user: string | null
  data?: AuthenticatedRolesQuery
  permissions?: AuthRolesFragmentFragment
}

const AuthContext = React.createContext<SAuthContext | null>(null)

export const AuthProvider: FC<PropsWithChildren> = ({ children }) => {
  const { data, loading } = useAuthenticatedRolesQuery()

  const permissions = data?.authenticatedItem?.role as AuthRolesFragmentFragment
  const isAuthenticated = loading
    ? 'loading'
    : // eslint-disable-next-line sonarjs/no-nested-conditional
      permissions
      ? 'authenticated'
      : 'unauthenticated'
  debugLog('render AuthProvider', data, 'loading', loading, {
    isAuthenticated,
    permissions,
  })
  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user: data?.authenticatedItem?.name ?? null,
        data,
        permissions,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
